#property strict

//+------------------------------------------------------------------+
//| TestTradingPipelineContainerLoggerDecorator.mqh                 |
//| TradingPipelineContainerLoggerDecorator 單元測試                |
//| 測試容器日誌裝飾者的裝飾者模式和日誌記錄功能                     |
//+------------------------------------------------------------------+

#include "../TestFramework.mqh"
#include "../../feature/TradingPipelineContainerLoggerDecorator.mqh"
#include "../../MQL4Logger/FileLog.mqh"

//+------------------------------------------------------------------+
//| 模擬流水線類                                                     |
//+------------------------------------------------------------------+
class MockTradingPipelineForDecorator : public ITradingPipeline
{
private:
    string m_name;
    string m_type;
    bool m_executed;

public:
    MockTradingPipelineForDecorator(string name = "MockPipeline", string type = "Mock")
        : m_name(name), m_type(type), m_executed(false) {}

    virtual ~MockTradingPipelineForDecorator() {}

    virtual void Execute() override { m_executed = true; }
    virtual string GetName() override { return m_name; }
    virtual string GetType() override { return m_type; }
    virtual bool IsExecuted() override { return m_executed; }
    virtual void Restore() override { m_executed = false; }
};

//+------------------------------------------------------------------+
//| 模擬日誌記錄器類                                                 |
//+------------------------------------------------------------------+
class MockFileLogForDecorator : public CFileLog
{
private:
    string m_lastMessage;
    string m_lastLevel;
    int m_messageCount;

public:
    MockFileLogForDecorator() : CFileLog("test.log", WARNING, false, false), m_lastMessage(""), m_lastLevel(""), m_messageCount(0) {}
    virtual ~MockFileLogForDecorator() {}

    virtual void Info(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "INFO";
        m_messageCount++;
    }

    virtual void Debug(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "DEBUG";
        m_messageCount++;
    }

    virtual void Warning(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "WARNING";
        m_messageCount++;
    }

    virtual void Error(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "ERROR";
        m_messageCount++;
    }

    // 測試輔助方法
    string GetLastMessage() const { return m_lastMessage; }
    string GetLastLevel() const { return m_lastLevel; }
    int GetMessageCount() const { return m_messageCount; }
    void Reset() { m_lastMessage = ""; m_lastLevel = ""; m_messageCount = 0; }
};

//+------------------------------------------------------------------+
//| TradingPipelineContainerLoggerDecorator 測試類                  |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerLoggerDecorator : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineContainerLoggerDecorator(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineContainerLoggerDecorator"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineContainerLoggerDecorator() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineContainerLoggerDecorator 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestDecoratorPattern();
        TestLoggingFunctionality();
        TestExecutionWithLogging();
        TestDetailedLoggingControl();

        Print("=== TradingPipelineContainerLoggerDecorator 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineContainerLoggerDecorator 構造函數 ---");

        MockFileLogForDecorator* logger = new MockFileLogForDecorator();
        TradingPipelineContainerLoggerDecorator* decorator = new TradingPipelineContainerLoggerDecorator(
            "TestContainer", "測試容器", logger, "LoggerDecorator", false, 10, true);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestConstructor - 實例創建",
                decorator != NULL,
                decorator != NULL ? "裝飾者實例創建成功" : "裝飾者實例創建失敗"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestConstructor - 名稱設置",
                decorator.GetName() == "TestContainer",
                "容器名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestConstructor - 類型設置",
                decorator.GetType() == "LoggerDecorator",
                "容器類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestConstructor - 日誌器設置",
                decorator.GetLogger() == logger,
                "日誌器設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestConstructor - 詳細日誌啟用",
                decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已啟用"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineContainerLoggerDecorator 基本屬性 ---");

        MockFileLogForDecorator* logger = new MockFileLogForDecorator();
        TradingPipelineContainerLoggerDecorator* decorator = new TradingPipelineContainerLoggerDecorator(
            "PropTest", "屬性測試", logger);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestBasicProperties - 初始執行狀態",
                !decorator.IsExecuted(),
                "初始執行狀態為未執行"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestBasicProperties - 初始流水線數量",
                decorator.GetPipelineCount() == 0,
                "初始流水線數量為0"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestBasicProperties - 容器為空",
                decorator.IsEmpty(),
                "容器初始為空"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試裝飾者模式
    void TestDecoratorPattern()
    {
        Print("--- 測試 TradingPipelineContainerLoggerDecorator 裝飾者模式 ---");

        MockFileLogForDecorator* logger = new MockFileLogForDecorator();
        TradingPipelineContainerLoggerDecorator* decorator = new TradingPipelineContainerLoggerDecorator(
            "DecoratorTest", "裝飾者測試", logger);

        MockTradingPipelineForDecorator* pipeline = new MockTradingPipelineForDecorator("TestPipeline");

        // 測試添加流水線功能
        bool addResult = decorator.AddPipeline(pipeline);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestDecoratorPattern - 添加流水線",
                addResult && decorator.GetPipelineCount() == 1,
                "裝飾者保持原有功能"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestDecoratorPattern - 流水線檢查",
                decorator.HasPipeline(pipeline),
                "流水線檢查功能正常"
            ));
        }

        delete decorator;
        delete pipeline;
        delete logger;
    }

    // 測試日誌記錄功能
    void TestLoggingFunctionality()
    {
        Print("--- 測試 TradingPipelineContainerLoggerDecorator 日誌記錄功能 ---");

        MockFileLogForDecorator* logger = new MockFileLogForDecorator();
        TradingPipelineContainerLoggerDecorator* decorator = new TradingPipelineContainerLoggerDecorator(
            "LogTest", "日誌測試", logger);

        // 重置日誌計數
        logger.Reset();

        // 執行容器（應該觸發日誌記錄）
        decorator.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestLoggingFunctionality - 日誌記錄觸發",
                logger.GetMessageCount() > 0,
                "執行時觸發了日誌記錄"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestLoggingFunctionality - 日誌內容包含容器名",
                StringFind(logger.GetLastMessage(), "LogTest") >= 0,
                "日誌內容包含容器名稱"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試帶日誌的執行
    void TestExecutionWithLogging()
    {
        Print("--- 測試 TradingPipelineContainerLoggerDecorator 帶日誌的執行 ---");

        MockFileLogForDecorator* logger = new MockFileLogForDecorator();
        TradingPipelineContainerLoggerDecorator* decorator = new TradingPipelineContainerLoggerDecorator(
            "ExecTest", "執行測試", logger, "LoggerDecorator", false, 10, true);

        MockTradingPipelineForDecorator* pipeline = new MockTradingPipelineForDecorator("ExecPipeline");
        decorator.AddPipeline(pipeline);

        logger.Reset();

        // 執行容器
        decorator.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestExecutionWithLogging - 容器執行",
                decorator.IsExecuted(),
                "容器執行成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestExecutionWithLogging - 流水線執行",
                pipeline.IsExecuted(),
                "子流水線執行成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestExecutionWithLogging - 詳細日誌記錄",
                logger.GetMessageCount() >= 2,
                "記錄了多條詳細日誌"
            ));
        }

        delete decorator;
        delete pipeline;
        delete logger;
    }

    // 測試詳細日誌控制
    void TestDetailedLoggingControl()
    {
        Print("--- 測試 TradingPipelineContainerLoggerDecorator 詳細日誌控制 ---");

        MockFileLogForDecorator* logger = new MockFileLogForDecorator();
        TradingPipelineContainerLoggerDecorator* decorator = new TradingPipelineContainerLoggerDecorator(
            "ControlTest", "控制測試", logger, "LoggerDecorator", false, 10, false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestDetailedLoggingControl - 詳細日誌禁用",
                !decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已禁用"
            ));
        }

        // 啟用詳細日誌
        decorator.SetDetailedLogging(true);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerLoggerDecorator::TestDetailedLoggingControl - 詳細日誌啟用",
                decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已啟用"
            ));
        }

        delete decorator;
        delete logger;
    }
};
